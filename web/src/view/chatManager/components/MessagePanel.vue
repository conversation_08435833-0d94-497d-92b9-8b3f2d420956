<template>
  <div class="message-panel">
    <!-- 聊天头部 -->
    <div class="chat-header">
      <div class="header-info">
        <el-avatar :size="30" :src="conversation.avatar">
          <el-icon v-if="conversation.type === 'group'"><ChatLineRound /></el-icon>
          <el-icon v-else><User /></el-icon>
        </el-avatar>
        <div class="info-text">
          <div class="name">{{ conversation.name }}</div>
          <!-- <div class="status" v-if="conversation.type === 'friend'">
            <span :class="conversation.online ? 'online' : 'offline'">
              {{ conversation.online ? '在线' : '离线' }}
            </span>
          </div> -->
        </div>
      </div>
    </div>

    <!-- 消息列表 -->
    <div class="message-list" ref="messageListRef">
      <el-scrollbar ref="scrollbarRef" class="message-scrollbar" @scroll="handleScroll">
        <div class="messages-container">
          <!-- 加载更多指示器 -->
          <div v-if="isLoadingMore" class="loading-more">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>加载更多消息...</span>
          </div>

          <!-- 没有更多消息提示 -->
          <div v-else-if="!hasMoreMessages && messages.length > 0" class="no-more-messages">
            没有更多消息了
          </div>

          <div class="message-content">
            <div v-for="(messageGroup, date) in groupedMessages" :key="date" class="message-group">
              <div class="date-divider">{{ date }}</div>
              <div v-for="message in messageGroup" :key="message.id" class="message-item">
                <MessageItem
                  :message="message"
                  :is-own="message.isOwn"
                  @avatar-click="handleAvatarClick"
                />
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <!-- 消息输入框 -->
    <div class="message-input">
      <MessageEditor @send="handleSendMessage" @send-image="handleSendImageMessage" @send-voice="handleSendVoiceMessage" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, watch, onMounted, onUnmounted } from 'vue'
import MessageItem from './MessageItem.vue'
import MessageEditor from './MessageEditor.vue'
import { sendMessage as sendMessageToServer } from '@/utils/chatService.js'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { useUserStore } from '@/pinia/modules/user.js'
import { decryptAESBase64 } from '@/utils/decrypt.js'

defineOptions({
  name: 'MessagePanel'
})

const props = defineProps({
  conversation: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['start-chat'])

const messageListRef = ref()
const scrollbarRef = ref()
const userStore = useUserStore()

// 获取当前用户ID
const getCurrentUserId = () => {
  // 优先使用 formChatId
  if (userStore.formChatId) {
    return userStore.formChatId
  }

  // 然后尝试从用户store获取
  if (userStore.userInfo && (userStore.userInfo.ID || userStore.userInfo.uuid || userStore.userInfo.id)) {
    return userStore.userInfo.ID || userStore.userInfo.uuid || userStore.userInfo.id
  }

  // 最后从localStorage/sessionStorage获取
  const storedUserId = localStorage.getItem('userId') || sessionStorage.getItem('userId')
  if (storedUserId) {
    return storedUserId
  }

  // 如果都没有，返回null
  console.warn('无法获取当前用户ID，用户可能未登录')
  return null
}

const currentUserId = computed(() => getCurrentUserId())

// 消息数据
const messages = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const hasMoreMessages = ref(true)
const isLoadingMore = ref(false)

// 加载聊天消息（初始加载）
const loadMessages = async (reset = true) => {
  try {
    if (!props.conversation) {
      console.log('没有选中的会话，跳过加载消息')
      return
    }

    // 根据会话类型确定chatId和聊天类型
    let chatId, chatType
    if (props.conversation.type === 'group') {
      // 群聊：chatId是群组ID
      chatId = (props.conversation.originalData?.id || props.conversation.originalData?.ID)?.toString()
      chatType = 'group'
    } else {
      // 私聊：chatId是对方用户ID（fromid）
      const targetUserId = props.conversation.originalData?.id || props.conversation.originalData?.ID

      if (!targetUserId) {
        console.error('无法获取私聊目标用户ID')
        return
      }

      chatId = targetUserId.toString()
      chatType = 'private'
    }

    if (!chatId) {
      console.log('无法获取chatId，跳过加载消息')
      return
    }

    console.log('开始加载聊天消息:', {
      chatId,
      chatIdType: typeof chatId,
      chatType,
      conversationType: props.conversation.type,
      conversationId: props.conversation.id,
      originalData: props.conversation.originalData
    })

    if (reset) {
      // 重置分页状态
      currentPage.value = 1
      hasMoreMessages.value = true
      messages.value = []
      
      // 清空该聊天的未读数量
      try {
        const { clearUnreadCount } = await import('@/utils/chatListManager.js')
        await clearUnreadCount(chatId)
      } catch (error) {
        console.warn('清空未读数量失败:', error)
      }
    }

    // 确保数据库已初始化
    const { openDb, getChatMessages } = await import('@/utils/db.js')
    await openDb()

    // 从数据库加载消息（使用新的聊天类型参数）
    const dbMessages = await getChatMessages(chatId, currentPage.value, pageSize.value, chatType)
    console.log('从数据库加载的消息:', dbMessages)

    if (!dbMessages || dbMessages.length === 0) {
      console.log('数据库中没有找到消息')

      // 调试：查看数据库中的所有内容
      console.log('=== 开始调试数据库内容 ===')
      const { debugDatabaseContent } = await import('@/utils/db.js')
      const { debugChatListContent } = await import('@/utils/chatListManager.js')

      await debugDatabaseContent()
      await debugChatListContent()
      console.log('=== 调试完成 ===')

      if (reset) {
        messages.value = []
      }
      hasMoreMessages.value = false
      return
    }

    // 确保消息有正确的头像信息
    const { processAvatarUrl, getDefaultAvatar } = await import('@/utils/avatarService.js')
    const processedMessages = dbMessages.map(msg => ({
      ...msg,
      avatar: processAvatarUrl(msg.avatar) || getDefaultAvatar(),
      senderAvatar: processAvatarUrl(msg.senderAvatar) || getDefaultAvatar()
    }))
    console.log('处理后的消息：', processedMessages)

    // 转换消息格式
    const convertedMessages = processedMessages.map(msg => {
      const fromidStr = msg.fromid?.toString()
      const currentUserIdStr = currentUserId.value?.toString()
      const isOwn = fromidStr === currentUserIdStr
      

      return {
        id: msg.id,
        msg: msg.msg,
        content: msg.msg,
        lastMessage: msg.lastMessage,
        userId: fromidStr,
        nickname: isOwn ? '我' : (msg.senderNickname || msg.nickname || `用户${msg.fromid}`),
        avatar: msg.avatar, // 使用处理过的头像URL
        senderAvatar: msg.senderAvatar, // 使用处理过的头像URL
        type: getMessageType(msg.typecode2),
        createdAt: msg.timestamp ? new Date(msg.timestamp) : new Date(msg.t),
        isOwn: isOwn,
        status: msg.isRedRead ? 'read' : 'sent'
      }
    })

    if (reset) {
      messages.value = convertedMessages
    } else {
      // 分页加载时，将历史消息添加到顶部
      messages.value = [...convertedMessages, ...messages.value]
    }

    // 检查是否还有更多消息
    if (dbMessages.length < pageSize.value) {
      hasMoreMessages.value = false
    }

    console.log('转换后的消息数量:', messages.value.length)
    console.log('转换后的消息数量:', messages.value)

    // 延迟滚动到底部（仅初始加载时）
    if (reset) {
      nextTick(() => {
        scrollToBottom()
      })
    }

  } catch (error) {
    console.error('加载聊天消息失败:', error)
    if (reset) {
      messages.value = []
    }
  }
}

// 加载更多消息（分页）
const loadMoreMessages = async () => {
  if (isLoadingMore.value || !hasMoreMessages.value) {
    return
  }

  try {
    isLoadingMore.value = true
    currentPage.value++

    // 记录当前滚动位置
    const messagesContainer = document.querySelector('.messages-container')
    const scrollHeight = messagesContainer?.scrollHeight || 0

    await loadMessages(false)

    // 恢复滚动位置（保持在加载前的位置）
    if (messagesContainer) {
      nextTick(() => {
        const newScrollHeight = messagesContainer.scrollHeight
        const scrollDiff = newScrollHeight - scrollHeight
        messagesContainer.scrollTop = scrollDiff
      })
    }

  } catch (error) {
    console.error('加载更多消息失败:', error)
    currentPage.value-- // 回退页码
  } finally {
    isLoadingMore.value = false
  }
}

// 根据typecode2获取消息类型
const getMessageType = (typecode2) => {
  switch (typecode2) {
    case 0: return 'text'
    case 1: return 'audio'
    case 2: return 'image'
    case 3: return 'voice'  // 语音消息
    case 4: return 'video'
    case 5: return 'forward'
    case 6: return 'retract'
    default: return 'text'
  }
}

// 按日期分组消息
const groupedMessages = computed(() => {
  const groups = {}
  messages.value.forEach(message => {
    const date = formatDate(message.createdAt)
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(message)
  })
  return groups
})

// 格式化日期
const formatDate = (date) => {
  const now = new Date()
  const messageDate = new Date(date)
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  const messageDay = new Date(messageDate.getFullYear(), messageDate.getMonth(), messageDate.getDate())

  if (messageDay.getTime() === today.getTime()) {
    return '今天'
  } else if (messageDay.getTime() === yesterday.getTime()) {
    return '昨天'
  } else {
    return messageDate.toLocaleDateString('zh-CN', { 
      month: 'long', 
      day: 'numeric' 
    })
  }
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (scrollbarRef.value) {
      const scrollbar = scrollbarRef.value
      const wrapRef = scrollbar.wrapRef
      wrapRef.scrollTop = wrapRef.scrollHeight
    }
  })
}



// 处理滚动事件
const handleScroll = (scrollInfo) => {
  // 当滚动到顶部附近时，加载更多消息
  if (scrollInfo.scrollTop < 100 && hasMoreMessages.value && !isLoadingMore.value) {
    loadMoreMessages()
  }
}

// 发送消息的通用函数
const sendMessage = async (content, messageType = 0) => {
  try {
    if (!props.conversation || !content.trim()) return

    const isGroup = props.conversation.type === 'group'
    let targetId

    if (isGroup) {
      // 优先使用小写的id，然后尝试大写的ID（向后兼容）
      targetId = props.conversation.originalData?.id || props.conversation.originalData?.ID
    } else {
      // 对于私聊，尝试多种方式获取目标ID
      if (props.conversation.originalData?.id) {
        targetId = props.conversation.originalData.id
      } else if (props.conversation.originalData?.ID) {
        targetId = props.conversation.originalData.ID
      } else if (props.conversation.originalData?.userId) {
        targetId = props.conversation.originalData.userId
      } else if (props.conversation.id && props.conversation.id.startsWith('friend_')) {
        targetId = parseInt(props.conversation.id.replace('friend_', ''))
      }
    }

    console.log('发送消息调试信息:', {
      conversation: props.conversation,
      isGroup,
      conversationId: props.conversation.id,
      originalData: props.conversation.originalData,
      targetId,
      targetIdType: typeof targetId,
      formChatId: userStore.formChatId,
      currentUserId: currentUserId.value,
      userInfo: userStore.userInfo
    })

    if (!targetId || isNaN(targetId)) {
      ElMessage.error(`无法获取目标ID，targetId: ${targetId}`)
      return
    }

    // 检查用户ID
    if (!currentUserId.value) {
      ElMessage.error('用户未登录，无法发送消息')
      return
    }

    // 构建消息参数
    const messageParams = {
      fromid: parseInt(currentUserId.value),
      toId: targetId,
      msg: content.trim(),
      typecode: isGroup ? 2 : 1, // 1-好友消息，2-群组消息
      typecode2: messageType, // 0-文本消息，2-图片消息，3-语音消息
      // ...(isGroup && { groupID: targetId })
    }

    console.log('发送消息参数:', messageParams)

    // 发送消息到服务器
    const result = await sendMessageToServer(messageParams)
    console.log('消息发送结果:', result)

    // 确定消息类型
    const messageTypeMap = {
      0: 'text',
      2: 'image',
      3: 'voice'
    }

    // 立即添加到本地消息列表（乐观更新）
    const newMessage = {
      id: Date.now(),
      userId: currentUserId.value,
      nickname: '我',
      avatar: '',
      content: content.trim(),
      type: messageTypeMap[messageType] || 'text',
      createdAt: new Date(),
      isOwn: true
    }

    messages.value.push(newMessage)
    scrollToBottom()

    // 发送消息时不再直接存储到数据库和更新聊天列表，等待WebSocket回显统一处理
    console.log('消息发送成功，等待WebSocket回显进行存储和聊天列表更新')

    // 发送消息后立即触发列表更新事件
    try {
      if (isGroup) {
        const groupId = props.conversation.originalData?.id || props.conversation.originalData?.ID
        if (groupId) {
          const updateEvent = new CustomEvent('groupMessageUpdate', {
            detail: {
              groupId: groupId,
              messageData: {
                msg: content.trim(),
                t: new Date().toISOString(),
                typecode2: messageType,
                senderNickname: '我'
              }
            }
          })
          window.dispatchEvent(updateEvent)
          console.log('发送消息后已发射群聊更新事件:', updateEvent.detail)
        }
      } else {
        // 私聊消息更新事件
        const updateEvent = new CustomEvent('privateMessageUpdate', {
          detail: {
            userId: targetId,
            messageData: {
              msg: content.trim(),
              t: new Date().toISOString(),
              typecode2: messageType,
              senderNickname: '我',
              content: content.trim(),
              timestamp: new Date().toISOString(),
              type: messageType
            }
          }
        })
        window.dispatchEvent(updateEvent)
        console.log('发送消息后已发射私聊更新事件:', updateEvent.detail)
      }
    } catch (error) {
      console.warn('发射发送消息更新事件失败:', error)
    }

  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败')
  }
}

// 发送文本消息
const handleSendMessage = async (content) => {
  await sendMessage(content, 0) // 0 表示文本消息
}

// 发送图片消息
const handleSendImageMessage = async (imageUrl) => {
  await sendMessage(imageUrl, 2) // 2 表示图片消息
}

// 发送语音消息
const handleSendVoiceMessage = async (voiceData) => {
  // voiceData 包含 { url, duration }
  const voiceMessage = JSON.stringify({
    url: voiceData.url,
    duration: voiceData.duration
  })
  await sendMessage(voiceMessage, 3) // 3 表示语音消息
}

// 头像点击
const handleAvatarClick = (message) => {
  console.log('点击头像:', message)

  // 如果是自己的消息，不处理
  if (message.isOwn) {
    return
  }

  // 构建私聊数据，格式与 GroupUserPanel 中的 startPrivateChat 一致
  // 获取用户ID，优先使用 userId，然后是 fromid 或 senderId
  const targetUserId = message.userId || message.fromid || message.senderId

  console.log('构建私聊数据调试信息:', {
    message,
    targetUserId,
    userId: message.userId,
    fromid: message.fromid,
    senderId: message.senderId
  })

  const privateChatData = {
    id: `friend_${targetUserId}`,
    type: 'friend',
    name: message.nickname || message.senderNickname || `用户${targetUserId}`,
    avatar: message.avatar || message.senderAvatar || '',
    lastMessage: '开始聊天',
    lastTime: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
    unread: 0,
    online: true,
    originalData: {
      ID: parseInt(targetUserId), // 确保ID是数字类型
      userId: parseInt(targetUserId),
      nickname: message.nickname || message.senderNickname,
      avatar: message.avatar || message.senderAvatar,
      phone: message.phone || ''
    }
  }

  // 触发开始私聊事件，传递给父组件处理
  emit('start-chat', privateChatData)
}

// 实时消息更新监听器
let messageUpdateListener = null

// 设置实时消息更新监听
const setupMessageUpdateListener = () => {
  messageUpdateListener = (event) => {
    console.log('收到实时消息更新事件:', event.detail)

    // 检查是否是当前会话的消息
    if (!props.conversation) return

    const { chatId, message } = event.detail

    // 确定当前会话的chatId
    let currentChatId
    if (props.conversation.type === 'group') {
      // 群聊：chatId是群组ID
      currentChatId = (props.conversation.originalData?.id || props.conversation.originalData?.ID)?.toString()
    } else {
      // 私聊：chatId是对方用户ID（fromid）
      const targetUserId = props.conversation.originalData?.id || props.conversation.originalData?.ID
      currentChatId = targetUserId?.toString()
    }

    // 检查是否是当前会话的消息
    if (currentChatId === chatId) {
      console.log('收到当前会话的新消息，实时显示:', message)

      // 获取当前用户ID用于判断是否是自己发送的消息
      const currentUserId = localStorage.getItem('formChatId') || sessionStorage.getItem('formChatId')
      const isOwnMessage = message.fromid && String(message.fromid) === String(currentUserId)

      // 直接添加新消息到消息列表，实现实时显示
      const newMessage = {
        id: message.id || Date.now(),
        content: message.msg || message.content || '',
        type: (message.typecode2 === 0 || message.type === 'text') ? 'text' : 'other',
        timestamp: message.t || new Date().toISOString(),
        createdAt: new Date(message.t || new Date()),
        userId: message.fromid,
        nickname: message.senderNickname || message.nickname || `用户${message.fromid}`,
        avatar: message.senderAvatar || message.avatar || '',
        isOwn: isOwnMessage,
        sender: {
          id: message.fromid,
          name: message.senderNickname || message.nickname || `用户${message.fromid}`,
          avatar: message.senderAvatar || message.avatar || ''
        },
        self: isOwnMessage
      }

      // 检查消息是否已存在（避免重复添加）
      const existingMessageIndex = messages.value.findIndex(msg =>
        msg.id === newMessage.id ||
        (msg.timestamp === newMessage.timestamp && msg.content === newMessage.content && msg.userId === newMessage.userId)
      )

      if (existingMessageIndex === -1) {
        // 添加到消息列表末尾
        messages.value.push(newMessage)

        // 滚动到底部显示新消息
        nextTick(() => {
          scrollToBottom()
        })

        console.log('新消息已实时显示在聊天界面:', newMessage)
      } else {
        console.log('消息已存在，跳过重复添加:', newMessage)
      }
    }
  }

  // 添加事件监听器
  window.addEventListener('newMessageReceived', messageUpdateListener)
  console.log('MessagePanel已添加实时消息更新监听器')
}

// 移除消息监听
const removeMessageUpdateListener = () => {
  if (messageUpdateListener) {
    window.removeEventListener('newMessageReceived', messageUpdateListener)
    messageUpdateListener = null
    console.log('MessagePanel已移除实时消息更新监听器')
  }
}

// 组件挂载时设置监听器
onMounted(() => {
  console.log('MessagePanel组件挂载，当前会话:', props.conversation)
  setupMessageUpdateListener()
  if (props.conversation) {
    loadMessages(true)
  }
})

// 组件卸载时移除监听器
onUnmounted(() => {
  removeMessageUpdateListener()
})

// 监听会话变化，重新加载消息
watch(() => props.conversation, (newConversation) => {
  if (newConversation) {
    console.log('会话切换，重新加载消息:', newConversation)
    // 重置状态并加载消息
    loadMessages(true)
  }
}, { immediate: true })

// 监听消息变化，滚动到底部（仅在非分页加载时）
watch(messages, (newMessages, oldMessages) => {
  // 如果是新增消息（不是分页加载），则滚动到底部
  if (newMessages.length > oldMessages.length && !isLoadingMore.value) {
    scrollToBottom()
  }
}, { deep: true })
</script>

<style lang="scss" scoped>
.message-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #374151;
}

.chat-header {
  padding: 13px 20px;
  background: #4b5563;
  border-bottom: 1px solid #6b7280;

  .header-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .info-text {
      .name {
        font-size: 16px;
        font-weight: 500;
        color: #f3f4f6;
        margin-bottom: 2px;
      }

      .status {
        font-size: 12px;

        .online {
          color: #22c55e;
        }

        .offline {
          color: #9ca3af;
        }
      }
    }
  }
}

.message-list {
  flex: 1;
  overflow: hidden;
  // height: calc(750px - 120px - 80px); // 总高度 - 头部 - 输入框
  // min-height: calc(750px - 120px - 80px);
  // max-height: calc(750px - 120px - 80px);

  .message-scrollbar {
    height: 100%;
  }

  .message-content {
    padding: 20px;
  }

  .message-group {
    margin-bottom: 20px;

    .date-divider {
      text-align: center;
      color: #9ca3af;
      font-size: 12px;
      margin-bottom: 15px;
      position: relative;

      /* &::before,
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        width: 30%;
        height: 1px;
        background: #6b7280;
      }
 */
      &::before {
        left: 0;
      }

      &::after {
        right: 0;
      }
    }

    .message-item {
      margin-bottom: 15px;
    }
  }

  .loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px;
    color: #9ca3af;
    font-size: 14px;
    gap: 8px;

    .el-icon {
      font-size: 16px;
    }
  }

  .no-more-messages {
    text-align: center;
    padding: 15px;
    color: #6b7280;
    font-size: 12px;
  }
}

.message-input {
  background: #4b5563;
  border-top: 1px solid #6b7280;
}
</style>
