<template>
  <div class="test-page">
    <h2>聊天弹窗测试页面</h2>
    <p>✅ 左侧会话列表已调整为竖向排列，会话改为群聊</p>
    <p>✅ 弹窗高度固定为700px</p>
    <p>✅ 群聊/好友切换逻辑：切换到好友显示空状态，切换到群聊显示唯一群聊</p>
    <p>✅ 样式已迁移到Tailwind CSS，使用正确的:deep()穿透语法</p>
    <p>✅ 深色主题优化完成：WeChat风格的深色界面，包含所有组件的主题适配</p>
    <p>🔧 <strong>最新修复：</strong></p>
    <p>✅ 打开弹窗时自动从数据库查询最新的群聊和私聊数据</p>
    <p>✅ 切换群聊/私聊标签页时重新查询数据库数据</p>
    <p>✅ WebSocket消息接收后实时更新聊天列表和消息显示</p>
    <p>✅ 聊天界面停留时能实时显示新收到的消息</p>

    <el-button type="primary" @click="openChat">
      打开聊天弹窗
    </el-button>

    <el-button type="success" @click="openChatWithUser">
      打开聊天弹窗（带用户信息）
    </el-button>
    
    <ChatDialog 
      v-model="dialogVisible" 
      :user="testUser"
      @close="handleClose"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ChatDialog from './ChatDialog.vue'

defineOptions({
  name: 'ChatTest'
})

const dialogVisible = ref(false)
const testUser = ref(null)

const openChat = () => {
  testUser.value = null
  dialogVisible.value = true
}

const openChatWithUser = () => {
  testUser.value = {
    id: 1,
    name: '测试用户',
    headImg: '',
    online: true
  }
  dialogVisible.value = true
}

const handleClose = () => {
  dialogVisible.value = false
  testUser.value = null
}
</script>

<style scoped>
.test-page {
  padding: 20px;
}

.test-page h2 {
  margin-bottom: 20px;
}

.el-button {
  margin-right: 10px;
}
</style>
